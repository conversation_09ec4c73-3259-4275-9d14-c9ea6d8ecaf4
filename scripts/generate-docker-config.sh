#!/bin/bash

# Script to generate base64 encoded Docker config for Kubernetes secret
# Usage: ./generate-docker-config.sh [username] [password] [email]

USERNAME="${1:-aaaa}"
PASSWORD="${2:-bbbb}"
EMAIL="${3:-<EMAIL>}"

echo "Generating Docker config for:"
echo "Username: $USERNAME"
echo "Email: $EMAIL"
echo ""

# Create base64 encoded auth string (username:password)
AUTH=$(echo -n "$USERNAME:$PASSWORD" | base64 -w 0)

# Create the Docker config JSON
DOCKER_CONFIG=$(cat <<EOF
{
  "auths": {
    "https://index.docker.io/v1/": {
      "username": "$USERNAME",
      "password": "$PASSWORD",
      "email": "$EMAIL",
      "auth": "$AUTH"
    }
  }
}
EOF
)

# Base64 encode the entire config
ENCODED_CONFIG=$(echo -n "$DOCKER_CONFIG" | base64 -w 0)

echo "Generated Docker config (base64 encoded):"
echo "$ENCODED_CONFIG"
echo ""
echo "Use this value in your dockerhub-secret.yaml file under data:.dockerconfigjson"
