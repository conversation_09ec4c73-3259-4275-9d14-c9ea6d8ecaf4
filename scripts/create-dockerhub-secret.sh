#!/bin/bash

# Create DockerHub secret for private image authentication
# Usage: ./create-dockerhub-secret.sh [username] [password] [email]

DOCKERHUB_USERNAME="${1:-aaaa}"
DOCKERHUB_PASSWORD="${2:-bbbb}"
DOCKERHUB_EMAIL="${3:-<EMAIL>}"
SECRET_NAME="dockerhub-registrykey"
NAMESPACE="blacking-ops-dev"

echo "Creating DockerHub secret with the following details:"
echo "Username: $DOCKERHUB_USERNAME"
echo "Email: $DOCKERHUB_EMAIL"
echo "Secret Name: $SECRET_NAME"
echo "Namespace: $NAMESPACE"
echo ""

# Create the secret
kubectl create secret docker-registry $SECRET_NAME \
  --docker-server=https://index.docker.io/v1/ \
  --docker-username=$DOCKERHUB_USERNAME \
  --docker-password="$DOCKERHUB_PASSWORD" \
  --docker-email=$DOCKERHUB_EMAIL \
  --namespace=$NAMESPACE

if [ $? -eq 0 ]; then
    echo "✅ DockerHub secret '$SECRET_NAME' created successfully in namespace '$NAMESPACE'"
    echo "The deployment.yaml has been updated to use this secret."
else
    echo "❌ Failed to create DockerHub secret"
    exit 1
fi
