#!/bin/bash

# Setup DockerHub authentication for private image pulling
# This script creates the secret and applies the deployment

set -e

DOCKERHUB_USERNAME="${1:-aaaa}"
DOCKERHUB_PASSWORD="${2:-bbbb}"
DOCKERHUB_EMAIL="${3:-<EMAIL>}"
SECRET_NAME="dockerhub-registrykey"
NAMESPACE="blacking-ops-dev"

echo "🚀 Setting up DockerHub authentication for private images"
echo "=================================================="
echo "Username: $DOCKERHUB_USERNAME"
echo "Email: $DOCKERHUB_EMAIL"
echo "Secret Name: $SECRET_NAME"
echo "Namespace: $NAMESPACE"
echo ""

# Check if namespace exists
echo "📋 Checking if namespace exists..."
if ! kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
    echo "⚠️  Namespace '$NAMESPACE' does not exist. Creating it..."
    kubectl create namespace $NAMESPACE
    echo "✅ Namespace '$NAMESPACE' created"
else
    echo "✅ Namespace '$NAMESPACE' already exists"
fi

# Delete existing secret if it exists
echo ""
echo "🗑️  Checking for existing secret..."
if kubectl get secret $SECRET_NAME -n $NAMESPACE >/dev/null 2>&1; then
    echo "⚠️  Secret '$SECRET_NAME' already exists. Deleting it..."
    kubectl delete secret $SECRET_NAME -n $NAMESPACE
    echo "✅ Existing secret deleted"
fi

# Create the DockerHub secret
echo ""
echo "🔐 Creating DockerHub secret..."
kubectl create secret docker-registry $SECRET_NAME \
  --docker-server=https://index.docker.io/v1/ \
  --docker-username=$DOCKERHUB_USERNAME \
  --docker-password="$DOCKERHUB_PASSWORD" \
  --docker-email=$DOCKERHUB_EMAIL \
  --namespace=$NAMESPACE

if [ $? -eq 0 ]; then
    echo "✅ DockerHub secret '$SECRET_NAME' created successfully"
else
    echo "❌ Failed to create DockerHub secret"
    exit 1
fi

# Verify the secret
echo ""
echo "🔍 Verifying secret..."
kubectl get secret $SECRET_NAME -n $NAMESPACE -o yaml

echo ""
echo "🚢 Applying deployment..."
kubectl apply -f manifest/deployment.yaml

echo ""
echo "✅ Setup complete! Your deployment should now be able to pull private images from DockerHub."
echo ""
echo "To check the status:"
echo "  kubectl get pods -n $NAMESPACE"
echo "  kubectl describe pod <pod-name> -n $NAMESPACE"
