# Create Project with Template API

This document describes how to use the `/projects/template` endpoint to create a new project from an existing template.

## Endpoint

```
POST /api/v1/projects/template
```

## Authentication

This endpoint requires authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Request Body

```json
{
  "template_id": 123,
  "name": "My New Project"
}
```

### Parameters

- `template_id` (uint64, required): The ID of the template project to use as a base
- `name` (string, required): The name for the new project (2-50 characters)

## Response

### Success Response (201 Created)

```json
{
  "status": true,
  "message": "Project created from template successfully",
  "data": {
    "id": 456,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "name": "My New Project",
    "slug": "my-new-project",
    "is_active": true,
    "type": "draft",
    "cluster": {
      "id": 1,
      "name": "production-cluster"
    },
    "deployments": [...],
    "services": [...],
    "ingress": [...]
  }
}
```

### Error Responses

#### 400 Bad Request - Invalid Request Format
```json
{
  "status": false,
  "message": "Invalid request format"
}
```

#### 400 Bad Request - Missing Template ID
```json
{
  "status": false,
  "message": "Template ID is required"
}
```

#### 400 Bad Request - Missing Project Name
```json
{
  "status": false,
  "message": "Project name is required"
}
```

#### 400 Bad Request - Invalid Project Name
```json
{
  "status": false,
  "message": "Invalid project name: unable to generate URL-friendly slug"
}
```

#### 400 Bad Request - Not a Template
```json
{
  "status": false,
  "message": "The specified namespace is not a template"
}
```

#### 404 Not Found - Template Not Found
```json
{
  "status": false,
  "message": "Template project not found"
}
```

## Behavior

1. **Template Retrieval**: The system retrieves the complete template project using the provided `template_id`
2. **Validation**: Ensures the specified namespace is actually a template (type = "template")
3. **Slug Generation**: Automatically generates a URL-friendly slug from the project name
4. **Data Copy**: Copies all template configuration including:
   - Deployments with their environments
   - Services with their ingress specifications
   - All other template settings
5. **Field Overrides**: Only these fields are modified in the new project:
   - `name`: Set to the provided name parameter
   - `slug`: Generated from the new name (e.g., "My Project" → "my-project")
   - `type`: Set to "draft" status
6. **Complete Creation**: Uses the existing project creation logic to ensure consistency

## Example Usage

### cURL Example

```bash
curl -X POST "http://localhost:8080/api/v1/projects/template" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "template_id": 123,
    "name": "My New Project"
  }'
```

### JavaScript Example

```javascript
const response = await fetch('/api/v1/projects/template', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    template_id: 123,
    name: 'My New Project'
  })
});

const result = await response.json();
console.log(result);
```

## Notes

- The new project will be created with `type: "draft"` regardless of the template's type
- All deployments, services, environments, and ingress specifications are copied exactly from the template
- The slug is automatically generated to be URL-friendly (lowercase, hyphens, no special characters)
- If the template has complex ingress configurations, they will be preserved in the new project
