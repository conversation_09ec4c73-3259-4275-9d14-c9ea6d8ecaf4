# Ingress Spec Bulk Host Update Service

## Overview

The `UpdateHostsByNamespace` service function allows you to bulk update the host field of all ingress-specs within a specific namespace by using the service name as the subdomain and combining it with the new domain.

## Function Signature

```go
func (s *IngressSpecService) UpdateHostsByNamespace(namespaceID uint64, newDomain string) (int, error)
```

## Parameters

- `namespaceID` (uint64): The ID of the namespace containing the ingress-specs to update
- `newDomain` (string): The new domain to replace the existing domain portion

## Return Values

- `int`: The number of ingress-specs that were successfully updated
- `error`: Any error that occurred during the operation

## Behavior

1. **Validation**: Validates that both `namespaceID` and `newDomain` are provided
2. **Namespace Check**: Verifies that the specified namespace exists
3. **Find Ingress Specs**: Retrieves all ingress-specs belonging to the namespace
4. **Host Update Logic**:
   - Uses the service name from the ingress-spec's associated service as the subdomain
   - Combines the service name with the new domain (e.g., service "api" + domain "example.com" = "api.example.com")
   - Skips ingress-specs where the service is not loaded or the service name is empty
5. **Bulk Update**: Updates each ingress-spec with the new host value
6. **Error Handling**: Returns partial success count if some updates fail

## Examples

### Example 1: Service Name-Based Domain Update

```go
// Update all ingress-specs in namespace 1 to use "new-dns.com"
// The service names will be used as subdomains
count, err := ingressSpecService.UpdateHostsByNamespace(1, "new-dns.com")
if err != nil {
    log.Printf("Error updating hosts: %v", err)
    return
}
log.Printf("Successfully updated %d ingress-specs", count)
```

**Result:**
- Service "api" → Host becomes `api.new-domain.com`
- Service "web-frontend" → Host becomes `web-frontend.new-domain.com`
- Service "admin-panel" → Host becomes `admin-panel.new-domain.com`

### Example 2: Different Domain

```go
count, err := ingressSpecService.UpdateHostsByNamespace(2, "example.com")
```

**Result:**
- Service "api" → Host becomes `api.example.com`
- Service "web" → Host becomes `web.example.com`

### Example 3: Error Handling

```go
count, err := ingressSpecService.UpdateHostsByNamespace(999, "new-dns.com")
if err != nil {
    if strings.Contains(err.Error(), "namespace not found") {
        log.Printf("Namespace 999 does not exist")
    } else {
        log.Printf("Unexpected error: %v", err)
    }
}
```

## Error Cases

1. **Invalid Parameters**:
   - `namespaceID` is 0: Returns "namespace ID is required"
   - `newDomain` is empty: Returns "new domain is required"

2. **Namespace Not Found**:
   - Returns "namespace not found" if the specified namespace doesn't exist

3. **Repository Errors**:
   - Returns detailed error messages for database operation failures
   - Includes the ingress-spec ID in update failure messages

4. **Partial Failures**:
   - If some updates succeed and others fail, returns the count of successful updates along with the error

## No-Op Cases

- If no ingress-specs are found for the namespace, the function returns `(0, nil)` - this is not considered an error
- Ingress-specs with no associated service or empty service names are skipped during processing

## Integration

This service function is part of the `IngressSpecService` and requires:
- `IngressSpecRepository` for finding and updating ingress-specs
- `NamespaceRepository` for namespace validation

The function is automatically available when using the service through dependency injection in the application.

## Testing

The function includes comprehensive unit tests covering:
- Successful bulk updates with multiple ingress-specs using service names
- Namespace validation
- Parameter validation
- Edge cases (missing services, empty service names, empty results)
- Error handling scenarios

Run tests with:
```bash
go test ./internal/core/services -v -run TestUpdateHostsByNamespace
```
