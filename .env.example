ENV=local
#ENV=production
#ENV=development

# Database Configuration
#DB_DRIVER=sqlite
#DB_HOST=localhost
#DB_PORT=5432
#DB_USER=user
#DB_PASSWORD=password
#DB_NAME=hexagonal_db
DB_DRIVER=
DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_NAME=
AUTO_MIGRATE=

# Server Configuration
PORT=8080

# JWT Configuration
JWT_SECRET=secret

DO_TOKEN=
TF_BACKEND_DB=
TF_BACKEND_SCHEMA_CLUSTER=cluster
TF_BACKEND_SCHEMA_RESOURCE=resource
TF_OPERATION_ENDPOINT=

CLOUDFLARE_API_TOKEN=
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_MASTER_ZONE_ID=

DOCKERHUB_USERNAME=
DOCKERHUB_PASSWORD=
DOCKERHUB_EMAIL=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=