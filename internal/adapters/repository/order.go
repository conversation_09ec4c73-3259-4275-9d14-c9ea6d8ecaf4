package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type OrderRepository struct {
	db *gorm.DB
}

func NewOrderRepository(db *gorm.DB) ports.OrderRepository {
	return &OrderRepository{db: db}
}

func (r *OrderRepository) Insert(order *domain.Order) error {
	err := r.db.Create(order).Error
	if err != nil {
		return err
	}

	// Reload the order with its relationships
	return r.db.Preload("User").
		Preload("User.UserType").
		Preload("Template").
		Preload("Template.Cluster").
		Preload("OrderDomains").
		Preload("OrderNamespaces").
		Preload("OrderNamespaces.Namespace").
		Preload("OrderNamespaces.Namespace.Ingress").
		Preload("OrderNamespaces.Namespace.Ingress.IngressSpecs").
		First(order, order.ID).Error
}

func (r *OrderRepository) FindByID(id uint64) (*domain.Order, error) {
	var order domain.Order
	err := r.db.Preload("User").
		Preload("User.UserType").
		Preload("Template").
		Preload("Template.Cluster").
		Preload("OrderDomains").
		Preload("OrderNamespaces").
		Preload("OrderNamespaces.Namespace").
		Preload("OrderNamespaces.Namespace.Ingress").
		Preload("OrderNamespaces.Namespace.Ingress.IngressSpecs").
		First(&order, id).Error
	if err != nil {
		return nil, err
	}
	return &order, nil
}

func (r *OrderRepository) FindAll(filter *ports.OrderFilter) ([]*domain.Order, error) {
	var orders []*domain.Order
	query := r.db.Preload("User").
		Preload("User.UserType").
		Preload("Template").
		Preload("Template.Cluster").
		Preload("OrderDomains").
		Preload("OrderNamespaces").
		Preload("OrderNamespaces.Namespace").
		Preload("OrderNamespaces.Namespace.Ingress").
		Preload("OrderNamespaces.Namespace.Ingress.IngressSpecs").
		Order("created_at DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.Code != nil {
			query = query.Where("code LIKE ?", "%"+*filter.Code+"%")
		}
		if filter.IsConfirmed != nil {
			query = query.Where("is_confirmed = ?", *filter.IsConfirmed)
		}
		if filter.UserID != nil {
			query = query.Where("user_id = ?", *filter.UserID)
		}
		if filter.TemplateID != nil {
			query = query.Where("template_id = ?", *filter.TemplateID)
		}
	}

	err := query.Find(&orders).Error
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (r *OrderRepository) Update(order *domain.Order) error {
	err := r.db.Save(order).Error
	if err != nil {
		return err
	}

	// Reload the order with its relationships
	return r.db.Preload("User").
		Preload("User.UserType").
		Preload("Template").
		Preload("Template.Cluster").
		Preload("OrderDomains").
		Preload("OrderNamespaces").
		Preload("OrderNamespaces.Namespace").
		Preload("OrderNamespaces.Namespace.Ingress").
		Preload("OrderNamespaces.Namespace.Ingress.IngressSpecs").
		First(order, order.ID).Error
}

func (r *OrderRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Order{}, id).Error
}
