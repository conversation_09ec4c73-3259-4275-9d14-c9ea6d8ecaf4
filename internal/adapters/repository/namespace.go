package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type NamespaceRepository struct {
	db *gorm.DB
}

func NewNamespaceRepository(db *gorm.DB) ports.NamespaceRepository {
	return &NamespaceRepository{db: db}
}

func (r *NamespaceRepository) Insert(namespace *domain.Namespace) error {
	err := r.db.Create(namespace).Error
	if err != nil {
		return err
	}

	// Reload the namespace with its relationships
	return r.db.Preload("Cluster").First(namespace, namespace.ID).Error
}

func (r *NamespaceRepository) FindByID(id uint64) (*domain.Namespace, error) {
	var namespace domain.Namespace
	err := r.db.Preload("Cluster.Status").
		Preload("Deployments").
		Preload("Deployments.Status").
		Preload("Deployments.Environments").
		Preload("Services").
		Preload("Services.Status").
		Preload("Services.IngressSpecs").
		Preload("Ingress").
		Preload("Ingress.Status").
		Preload("Ingress.IngressSpecs.Service").
		Preload("Domains", func(db *gorm.DB) *gorm.DB {
			return db.Order("domain.index ASC")
		}).
		First(&namespace, id).Error
	if err != nil {
		return nil, err
	}
	return &namespace, nil
}

func (r *NamespaceRepository) FindAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	var namespaces []*domain.Namespace
	query := r.db.Preload("Cluster.Status").
		Preload("Deployments").
		Preload("Deployments.Status").
		Preload("Deployments.Environments").
		Preload("Services").
		Preload("Services.Status").
		Preload("Services.IngressSpecs").
		Preload("Ingress").
		Preload("Ingress.Status").
		Preload("Ingress.IngressSpecs.Service").
		Order("updated_at DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.Slug != nil {
			query = query.Where("slug LIKE ?", "%"+*filter.Slug+"%")
		}
		if filter.ClusterID != nil {
			query = query.Where("cluster_id = ?", *filter.ClusterID)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.Type != nil {
			query = query.Where("type = ?", *filter.Type)
		}
	}

	err := query.Find(&namespaces).Error
	if err != nil {
		return nil, err
	}
	return namespaces, nil
}

func (r *NamespaceRepository) Update(namespace *domain.Namespace) error {
	return r.db.Save(namespace).Error
}

func (r *NamespaceRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Namespace{}, id).Error
}
