package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type DomainRepository struct {
	db *gorm.DB
}

func NewDomainRepository(db *gorm.DB) ports.DomainRepository {
	return &DomainRepository{db: db}
}

func (r *DomainRepository) Insert(domain *domain.Domain) error {
	err := r.db.Create(domain).Error
	if err != nil {
		return err
	}

	// Reload the dns with its relationships
	return r.db.Preload("Namespace").First(domain, domain.ID).Error
}

func (r *DomainRepository) FindByID(id uint64) (*domain.Domain, error) {
	var domain domain.Domain
	err := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		First(&domain, id).Error
	if err != nil {
		return nil, err
	}
	return &domain, nil
}

func (r *DomainRepository) FindAll(filter *ports.DomainFilter) ([]*domain.Domain, error) {
	var domains []*domain.Domain
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Order("index DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsDefault != nil {
			query = query.Where("is_default = ?", *filter.IsDefault)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
	}

	err := query.Find(&domains).Error
	if err != nil {
		return nil, err
	}
	return domains, nil
}

func (r *DomainRepository) Update(domain *domain.Domain) error {
	return r.db.Save(domain).Error
}

func (r *DomainRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Domain{}, id).Error
}
