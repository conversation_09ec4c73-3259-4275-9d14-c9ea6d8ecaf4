package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type OrderHandler struct {
	orderService ports.OrderService
}

func NewOrderHandler(orderService ports.OrderService) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
	}
}

func (h *OrderHandler) CreateOrder(c *fiber.Ctx) error {
	// Get user ID from JWT token
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var req dto.CreateOrderRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Convert order domains from DTO to service format
	var orderDomains []ports.CreateOrderDomainData
	for _, domain := range req.OrderDomains {
		orderDomains = append(orderDomains, ports.CreateOrderDomainData{
			Name:        domain.Name,
			IsAvailable: domain.IsAvailable,
		})
	}

	order, err := h.orderService.Create(req.Name, req.Code, req.Description, req.IsConfirmed, userID, req.TemplateID, orderDomains)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Order created successfully", dto.ToOrderDetailDTO(order))
}

func (h *OrderHandler) GetOrders(c *fiber.Ctx) error {
	// Parse query parameters
	filter := &ports.OrderFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if code := c.Query("code"); code != "" {
		filter.Code = &code
	}

	if isConfirmedStr := c.Query("is_confirmed"); isConfirmedStr != "" {
		if isConfirmed, err := strconv.ParseBool(isConfirmedStr); err == nil {
			filter.IsConfirmed = &isConfirmed
		}
	}

	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if userID, err := strconv.ParseUint(userIDStr, 10, 64); err == nil {
			filter.UserID = &userID
		}
	}

	if templateIDStr := c.Query("template_id"); templateIDStr != "" {
		if templateID, err := strconv.ParseUint(templateIDStr, 10, 64); err == nil {
			filter.TemplateID = &templateID
		}
	}

	orders, err := h.orderService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get orders")
	}

	var orderList []dto.OrderListItemResponse
	for _, order := range orders {
		orderList = append(orderList, *dto.ToOrderListItemDTO(order))
	}

	return response.Success(c, fiber.StatusOK, "Orders retrieved successfully", orderList)
}

func (h *OrderHandler) GetMyOrders(c *fiber.Ctx) error {
	// Get user ID from JWT token
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	filter := &ports.OrderFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if code := c.Query("code"); code != "" {
		filter.Code = &code
	}

	if isConfirmedStr := c.Query("is_confirmed"); isConfirmedStr != "" {
		if isConfirmed, err := strconv.ParseBool(isConfirmedStr); err == nil {
			filter.IsConfirmed = &isConfirmed
		}
	}

	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if userID, err := strconv.ParseUint(userIDStr, 10, 64); err == nil {
			filter.UserID = &userID
		}
	}

	if templateIDStr := c.Query("template_id"); templateIDStr != "" {
		if templateID, err := strconv.ParseUint(templateIDStr, 10, 64); err == nil {
			filter.TemplateID = &templateID
		}
	}

	filter.UserID = &userID

	orders, err := h.orderService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get orders")
	}

	var orderList []dto.OrderListItemResponse
	for _, order := range orders {
		orderList = append(orderList, *dto.ToOrderListItemDTO(order))
	}

	return response.Success(c, fiber.StatusOK, "My orders retrieved successfully", orderList)
}

func (h *OrderHandler) GetOrderByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	order, err := h.orderService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Order not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Order retrieved successfully", dto.ToOrderDetailDTO(order))
}

func (h *OrderHandler) UpdateOrderConfirmation(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateOrderConfirmationRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	order, err := h.orderService.UpdateConfirmation(id, req.IsConfirmed)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Order not found")
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Order confirmation updated successfully", dto.ToOrderDetailDTO(order))
}

func (h *OrderHandler) UpdateOrder(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateOrderRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	order, err := h.orderService.Update(id, req.Name, req.Code, req.Description, req.IsConfirmed, req.UserID, req.TemplateID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Order updated successfully", dto.ToOrderDetailDTO(order))
}

// Helper function to get user ID from JWT context
func (h *OrderHandler) getUserIDFromContext(c *fiber.Ctx) uint64 {
	userID := c.Locals("user_id")
	if userID == nil {
		return 0
	}

	if id, ok := userID.(uint64); ok {
		return id
	}

	return 0
}
