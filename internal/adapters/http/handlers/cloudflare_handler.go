package handlers

import (
	"github.com/gofiber/fiber/v2"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"
)

type CloudflareHandler struct {
	cloudflareService ports.CloudflareService
}

func NewCloudflareHandler(cloudflareService ports.CloudflareService) *CloudflareHandler {
	return &CloudflareHandler{cloudflareService: cloudflareService}
}

func (h *CloudflareHandler) GetAllZones(c *fiber.Ctx) error {
	cloudflareResponse, err := h.cloudflareService.GetAllZones()
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get zones")
	}
	return response.Success(c, fiber.StatusOK, "Zones retrieved successfully", cloudflareResponse)
}
