package http

import (
	"context"
	"fmt"
	"log"
	"ops-api/internal/adapters/repository"
	"ops-api/internal/core/services"

	"gorm.io/gorm"

	"ops-api/config"
	"ops-api/internal/adapters/http/handlers"
	"ops-api/internal/adapters/http/middleware"
	"ops-api/internal/adapters/http/routes"

	"github.com/gofiber/fiber/v2"
)

type Server struct {
	app  *fiber.App
	port string
}

func NewServer(
	db *gorm.DB,
	cfg *config.Config,
) *Server {
	// Create fiber app
	app := fiber.New()

	// Create auth middleware
	authMiddleware := middleware.NewAuthMiddleware(cfg.JWTSecret)

	// Initialize repositories
	userTypeRepo := repository.NewUserTypeRepository(db)
	userRepo := repository.NewUserRepository(db)
	workspaceRepo := repository.NewWorkspaceRepository(db)
	clusterRepo := repository.NewClusterRepository(db)
	namespaceRepo := repository.NewNamespaceRepository(db)
	domainRepo := repository.NewDomainRepository(db)
	deploymentRepo := repository.NewDeploymentRepository(db)
	environmentRepo := repository.NewEnvironmentRepository(db)
	serviceRepo := repository.NewServiceRepository(db)
	ingressRepo := repository.NewIngressRepository(db)
	ingressSpecRepo := repository.NewIngressSpecRepository(db)
	jobRepo := repository.NewJobRepository(db)
	jobLogRepo := repository.NewJobLogRepository(db)
	serverStatusRepo := repository.NewServerStatusRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	orderDomainRepo := repository.NewOrderDomainRepository(db)
	orderNamespaceRepo := repository.NewOrderNamespaceRepository(db)
	jobStatusRepo := repository.NewJobStatusRepository(db)

	// Initialize services
	userTypeService := services.NewUserTypeService(userTypeRepo)
	userService := services.NewUserService(userRepo, userTypeRepo, cfg.JWTSecret)
	workspaceService := services.NewWorkspaceService(workspaceRepo, userRepo)
	clusterService := services.NewClusterService(clusterRepo, workspaceRepo)
	cloudflareService := services.NewCloudflareService()
	namespaceService := services.NewNamespaceService(namespaceRepo, clusterRepo, workspaceRepo)
	deploymentService := services.NewDeploymentService(deploymentRepo, namespaceRepo, userRepo)
	environmentService := services.NewEnvironmentService(environmentRepo, deploymentRepo, deploymentService)
	serviceService := services.NewServiceService(serviceRepo, namespaceRepo)
	ingressService := services.NewIngressService(ingressRepo, namespaceRepo)
	ingressSpecService := services.NewIngressSpecService(ingressSpecRepo, serviceRepo, ingressRepo, namespaceRepo, serviceService, ingressService)
	dnsService := services.NewDnsService(namespaceService)
	orderService := services.NewOrderService(orderRepo, orderDomainRepo, userRepo, namespaceRepo)
	orderDomainService := services.NewOrderDomainService(orderDomainRepo, orderRepo, orderNamespaceRepo)
	orderNamespaceService := services.NewOrderNamespaceService(orderNamespaceRepo, orderRepo, namespaceRepo, workspaceRepo, clusterRepo)
	jobStatusService := services.NewJobStatusService(jobStatusRepo)
	jobService := services.NewJobService(jobRepo, jobLogRepo, jobStatusService)
	jobLogService := services.NewJobLogService(jobLogRepo, jobRepo)
	operationService := services.NewOperationService(clusterRepo, jobService, clusterService, deploymentService, serviceService, ingressService, namespaceService, dnsService, orderService, orderNamespaceService, domainRepo)
	serverStatusService := services.NewServerStatusService(serverStatusRepo)
	domainService := services.NewDomainService(domainRepo, namespaceRepo, ingressSpecService, dnsService, operationService, orderDomainService)
	projectService := services.NewProjectService(namespaceRepo, namespaceService, deploymentService, environmentService, serviceService, ingressService, ingressSpecService, clusterService, orderService, orderNamespaceService, domainService, dnsService, operationService)

	// Create handlers
	userHandler := handlers.NewUserHandler(userService, userTypeService, cfg.JWTSecret)
	userTypeHandler := handlers.NewUserTypeHandler(userTypeService)
	workspaceHandler := handlers.NewWorkspaceHandler(workspaceService)
	clusterHandler := handlers.NewClusterHandler(clusterService)
	cloudflareHandler := handlers.NewCloudflareHandler(cloudflareService)
	namespaceHandler := handlers.NewNamespaceHandler(namespaceService)
	domainHandler := handlers.NewDomainHandler(domainService)
	deploymentHandler := handlers.NewDeploymentHandler(deploymentService)
	environmentHandler := handlers.NewEnvironmentHandler(environmentService)
	serviceHandler := handlers.NewServiceHandler(serviceService)
	ingressHandler := handlers.NewIngressHandler(ingressService)
	ingressSpecHandler := handlers.NewIngressSpecHandler(ingressSpecService)
	projectHandler := handlers.NewProjectHandler(projectService)
	jobHandler := handlers.NewJobHandler(jobService)
	jobLogHandler := handlers.NewJobLogHandler(jobLogService)
	operationHandler := handlers.NewOperationHandler(operationService)
	healthHandler := handlers.NewHealthHandler()
	serverStatusHandler := handlers.NewServerStatusHandler(serverStatusService)
	jobStatusHandler := handlers.NewJobStatusHandler(jobStatusService)
	dnsHandler := handlers.NewDnsHandler(dnsService)
	orderHandler := handlers.NewOrderHandler(orderService)
	orderDomainHandler := handlers.NewOrderDomainHandler(orderDomainService)
	orderNamespaceHandler := handlers.NewOrderNamespaceHandler(orderNamespaceService)

	// Setup router config
	routerConfig := &routes.RouterConfig{
		UserHandler:           userHandler,
		UserTypeHandler:       userTypeHandler,
		WorkspaceHandler:      workspaceHandler,
		ClusterHandler:        clusterHandler,
		CloudflareHandler:     cloudflareHandler,
		NamespaceHandler:      namespaceHandler,
		DomainHandler:         domainHandler,
		DeploymentHandler:     deploymentHandler,
		EnvironmentHandler:    environmentHandler,
		ServiceHandler:        serviceHandler,
		IngressHandler:        ingressHandler,
		IngressSpecHandler:    ingressSpecHandler,
		ProjectHandler:        projectHandler,
		JobHandler:            jobHandler,
		JobLogHandler:         jobLogHandler,
		OperationHandler:      operationHandler,
		HealthHandler:         healthHandler,
		ServerStatusHandler:   serverStatusHandler,
		JobStatusHandler:      jobStatusHandler,
		DnsHandler:            dnsHandler,
		OrderHandler:          orderHandler,
		OrderDomainHandler:    orderDomainHandler,
		OrderNamespaceHandler: orderNamespaceHandler,
		AuthMiddleware:        authMiddleware.AuthRequired(),
	}

	// Setup routes
	routes.SetupRoutes(app, routerConfig)

	return &Server{
		app:  app,
		port: cfg.Port,
	}
}

func (s *Server) Start() error {
	log.Printf("Server starting on port %s", s.port)
	return s.app.Listen(":" + s.port)
}

func (s *Server) Shutdown(ctx context.Context) error {
	log.Println("Shutting down server...")

	// Create a channel to signal when shutdown is complete
	done := make(chan error, 1)

	go func() {
		done <- s.app.Shutdown()
	}()

	// Wait for shutdown to complete or context to timeout
	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return fmt.Errorf("shutdown timeout: %v", ctx.Err())
	}
}

func (s *Server) GetApp() *fiber.App {
	return s.app
}
