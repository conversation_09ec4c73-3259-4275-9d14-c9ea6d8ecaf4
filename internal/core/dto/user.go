package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateUserRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type UpdateUserRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type RegisterRequest struct {
	Name     string `json:"name" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=6"`
}

type CreateSaleUserRequest struct {
	Name     string `json:"name" validate:"required,min=2,max=100"`
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required,min=6"`
}

type AuthResponse struct {
	Token string             `json:"token"`
	User  UserDetailResponse `json:"user"`
}

type UserListItemResponse struct {
	ID        uint64                    `json:"id"`
	CreatedAt time.Time                 `json:"created_at"`
	UpdatedAt time.Time                 `json:"updated_at"`
	Name      string                    `json:"name"`
	Email     string                    `json:"email"`
	UserType  *UserTypeRelationResponse `json:"user_type,omitempty"`
}

type UserDetailResponse struct {
	ID         uint64                      `json:"id"`
	CreatedAt  time.Time                   `json:"created_at"`
	UpdatedAt  time.Time                   `json:"updated_at"`
	Name       string                      `json:"name"`
	Email      string                      `json:"email"`
	UserType   *UserTypeRelationResponse   `json:"user_type,omitempty"`
	Workspaces []WorkspaceRelationResponse `json:"workspaces,omitempty"`
}

type UserRelationResponse struct {
	ID       uint64                    `json:"id"`
	Name     string                    `json:"name"`
	Email    string                    `json:"email"`
	UserType *UserTypeRelationResponse `json:"user_type,omitempty"`
}

// Convert response

func ToUserListItemDTO(d *domain.User) *UserListItemResponse {
	return &UserListItemResponse{
		ID:        d.ID,
		CreatedAt: d.CreatedAt,
		UpdatedAt: d.UpdatedAt,
		Name:      d.Name,
		Email:     d.Email,
		UserType:  ToUserTypeRelationDTO(d.UserType),
	}
}

func ToUserDetailDTO(d *domain.User) *UserDetailResponse {
	var workspaces []WorkspaceRelationResponse
	for _, workspace := range d.Workspaces {
		workspaces = append(workspaces, *ToWorkspaceRelationDTO(&workspace))
	}

	return &UserDetailResponse{
		ID:         d.ID,
		CreatedAt:  d.CreatedAt,
		UpdatedAt:  d.UpdatedAt,
		Name:       d.Name,
		Email:      d.Email,
		UserType:   ToUserTypeRelationDTO(d.UserType),
		Workspaces: workspaces,
	}
}

func ToUserRelationDTO(d *domain.User) *UserRelationResponse {
	return &UserRelationResponse{
		ID:       d.ID,
		Name:     d.Name,
		Email:    d.Email,
		UserType: ToUserTypeRelationDTO(d.UserType),
	}
}
