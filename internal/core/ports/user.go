package ports

import "ops-api/internal/core/domain"

// UserFilter represents filters for querying users
type UserFilter struct {
	Name       *string
	Email      *string
	UserTypeID *uint64
	IsActive   *bool
}

type UserRepository interface {
	Insert(user *domain.User) error
	FindAll(filter *UserFilter) ([]*domain.User, error)
	FindByID(id uint) (*domain.User, error)
	FindByEmail(email string) (*domain.User, error)
	Update(user *domain.User) error
	Delete(id uint) error
}

type UserService interface {
	Register(name, email, password string) (*domain.User, error)
	Login(email, password string) (*domain.User, string, error)
	GetByID(id uint) (*domain.User, error)
	GetByEmail(email string) (*domain.User, error)
	GetProfile(userID uint) (*domain.User, error)
	UpdateProfile(userID uint, name, email string) (*domain.User, error)
	ChangePassword(userID uint, currentPassword, newPassword string) error
	GetAllUsers(filter *UserFilter) ([]*domain.User, error)
	DeleteUser(adminUserID, targetUserID uint) error
	CreateUser(adminUserID uint, name, email, password string, userTypeID uint64) (*domain.User, error)
	CreateSaleUser(name, email, password string) (*domain.User, error)
}
