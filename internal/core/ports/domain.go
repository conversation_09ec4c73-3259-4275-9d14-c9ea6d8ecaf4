package ports

import "ops-api/internal/core/domain"

// DomainFilter represents filters for querying domains
type DomainFilter struct {
	Name        *string
	IsDefault   *bool
	IsActive    *bool
	NamespaceID *uint64
}

type DomainRepository interface {
	Insert(domain *domain.Domain) error
	FindAll(filter *DomainFilter) ([]*domain.Domain, error)
	FindByID(id uint64) (*domain.Domain, error)
	Update(domain *domain.Domain) error
	Delete(id uint64) error
}

type DomainService interface {
	Create(name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64) (*domain.Domain, error)
	GetAll(filter *DomainFilter) ([]*domain.Domain, error)
	GetByID(id uint64) (*domain.Domain, error)
	GetDefaultByNamespaceID(namespaceID uint64) (*domain.Domain, error)
	Update(id uint64, name string, isDefault, isActive bool, zoneID, accountID, accountName string, namespaceID uint64, index int) (*domain.Domain, error)
	UpdateStatus(id uint64, isActive bool) (*domain.Domain, error)
	SetDefault(userID, id uint64, accessToken string) (*domain.Domain, error)
	Delete(id uint64) error
}
