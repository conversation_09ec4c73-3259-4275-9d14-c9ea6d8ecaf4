package ports

import "ops-api/internal/core/domain"

// Order<PERSON>ilter represents filters for querying orders
type OrderFilter struct {
	Name        *string
	Code        *string
	IsConfirmed *bool
	UserID      *uint64
	TemplateID  *uint64
}

type OrderRepository interface {
	Insert(order *domain.Order) error
	FindAll(filter *OrderFilter) ([]*domain.Order, error)
	FindByID(id uint64) (*domain.Order, error)
	Update(order *domain.Order) error
	Delete(id uint64) error
}

type OrderService interface {
	Create(name, code, description string, isConfirmed bool, userID, templateID uint64, orderDomains []CreateOrderDomainData) (*domain.Order, error)
	GetAll(filter *OrderFilter) ([]*domain.Order, error)
	GetByID(id uint64) (*domain.Order, error)
	Update(id uint64, name, code, description string, isConfirmed bool, userID, templateID uint64) (*domain.Order, error)
	UpdateConfirmation(id uint64, isConfirmed bool) (*domain.Order, error)
	Delete(id uint64) error
}

// CreateOrderDomainData represents the data needed to create an order domain
type CreateOrderDomainData struct {
	Name        string
	IsAvailable bool
}
