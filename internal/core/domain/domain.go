package domain

type Domain struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	IsDefault   bool   `json:"is_default"`
	IsActive    bool   `json:"is_active"`
	ZoneID      string `json:"zone_id" gorm:"not null"`
	AccountID   string `json:"account_id" gorm:"not null"`
	AccountName string `json:"account_name" gorm:"not null"`
	NamespaceID uint64 `json:"namespace_id" gorm:"not null"`
	Index       int    `json:"index"`

	// Relationships
	Namespace *Namespace `json:"namespace,omitempty" gorm:"foreignKey:NamespaceID"`
}
