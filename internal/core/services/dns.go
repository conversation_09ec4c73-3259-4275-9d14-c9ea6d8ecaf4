package services

import (
	"fmt"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
	"os/exec"
	"strconv"
)

type DnsService struct {
	namespaceService ports.NamespaceService
}

func NewDnsService(namespaceService ports.NamespaceService) *DnsService {
	return &DnsService{
		namespaceService: namespaceService,
	}
}

func (s *DnsService) HandleDnsAsync(accessToken string, req dto.HandleDnsRequest) (interface{}, error) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in HandleDnsAsync: %v\n", r)
			}
		}()
		_, err := s.Handle<PERSON>ns(accessToken, req)
		if err != nil {
			fmt.Printf("Async DNS handling failed: %v\n", err)
		} else {
			fmt.Println("Async DNS handling completed successfully")
		}
	}()

	//_, err := s.Handle<PERSON>ns(accessToken, req)
	//if err != nil {
	//	fmt.Printf("Async DNS handling failed: %v\n", err)
	//} else {
	//	fmt.Println("Async DNS handling completed successfully")
	//}

	return "DNS handling started.", nil
}

func (s *DnsService) HandleDns(accessToken string, req dto.HandleDnsRequest) (interface{}, error) {
	valueEndpoint := os.Getenv("TF_OPERATION_ENDPOINT")
	endpoint := fmt.Sprintf("%s/services/dns?namespace_id=%s", valueEndpoint, strconv.Itoa(int(req.NamespaceID)))
	terraformDir := "./terraform/dns"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/dns"
	}
	workspaceName := fmt.Sprintf("namespace-%s", strconv.Itoa(int(req.NamespaceID)))

	namespace, err := s.namespaceService.GetByID(req.NamespaceID)
	if err != nil {
		return nil, err
	}

	namespaceStatus := "inactive"
	if namespace.IsActive {
		namespaceStatus = "active"
	}

	// Get environment variables
	valueCloudflareApiToken := os.Getenv("CLOUDFLARE_API_TOKEN")

	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=endpoint=%s", endpoint),
		fmt.Sprintf("-var=access_token=%s", accessToken),
		fmt.Sprintf("-var=ip_address=%s", namespace.Cluster.LoadBalanceIP),
		fmt.Sprintf("-var=cloudflare_api_token=%s", valueCloudflareApiToken),
		fmt.Sprintf("-var=cloudflare_zone_id=%s", req.ZoneID),
		fmt.Sprintf("-var=namespace_status=%s", namespaceStatus),
	}

	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := os.Getenv("TF_BACKEND_SCHEMA_DNS")
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)
		return "terraform init failed", tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))
	// terraform select workspace or create workspace
	tfWorkspace := exec.Command("terraform", "workspace", "select", "-or-create", workspaceName)
	tfWorkspace.Dir = terraformDir
	tfWorkspaceOutput, tfWorkspaceErr := tfWorkspace.CombinedOutput()
	if tfWorkspaceErr != nil {
		fmt.Println("select or create workspace failed", string(tfWorkspaceOutput))
		return "select or create workspace failed", tfWorkspaceErr
	}
	fmt.Println("select or create workspace successful", string(tfWorkspaceOutput))

	// terraform apply
	if req.Method == "apply" {
		applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
		tfApply := exec.Command("terraform", applyArgs...)
		tfApply.Dir = terraformDir
		tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()
		if tfApplyErr != nil {
			fmt.Println("terraform apply failed", string(tfApplyOutput))
			return "terraform apply failed", tfApplyErr
		}
		fmt.Println("terraform apply successful", string(tfApplyOutput))
	} else if req.Method == "destroy" {
		destroyArgs := append([]string{"destroy", "-auto-approve"}, varFlags...)
		tfDestroy := exec.Command("terraform", destroyArgs...)
		tfDestroy.Dir = terraformDir
		tfDestroyOutput, tfDestroyErr := tfDestroy.CombinedOutput()
		if tfDestroyErr != nil {
			fmt.Printf("terraform destroy failed - output: %s\n", string(tfDestroyOutput))
			fmt.Printf("terraform destroy failed - error: %v\n", tfDestroyErr)
			return "terraform destroy failed", tfDestroyErr
		}
		fmt.Println("terraform destroy successful", string(tfDestroyOutput))
	} else {
		return "Invalid method specified. Use 'apply' or 'destroy'.", fmt.Errorf("invalid method: %s", req.Method)
	}

	return "terraform apply successful", nil
}
