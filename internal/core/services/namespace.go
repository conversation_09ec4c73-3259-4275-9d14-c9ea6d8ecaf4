package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type NamespaceService struct {
	namespaceRepo ports.NamespaceRepository
	clusterRepo   ports.ClusterRepository
	workspaceRepo ports.WorkspaceRepository
}

func NewNamespaceService(namespaceRepo ports.NamespaceRepository, clusterRepo ports.ClusterRepository, workspaceRepo ports.WorkspaceRepository) ports.NamespaceService {
	return &NamespaceService{
		namespaceRepo: namespaceRepo,
		clusterRepo:   clusterRepo,
		workspaceRepo: workspaceRepo,
	}
}

func (s *NamespaceService) Create(name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if slug == "" {
		return nil, errors.New("slug is required")
	}
	if clusterID == 0 {
		return nil, errors.New("cluster ID is required")
	}
	if Type == "" {
		return nil, errors.New("namespace type is required")
	}

	// Verify cluster exists
	_, err := s.clusterRepo.FindByID(clusterID, 0)
	if err != nil {
		return nil, errors.New("cluster not found")
	}

	namespace := &domain.Namespace{
		Name:      name,
		Slug:      slug,
		IsActive:  isActive,
		Type:      Type,
		ClusterID: clusterID,
	}

	err = s.namespaceRepo.Insert(namespace)
	if err != nil {
		return nil, err
	}

	return namespace, nil
}

func (s *NamespaceService) GetAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	return s.namespaceRepo.FindAll(filter)
}

func (s *NamespaceService) GetAllByUserWorkspaces(userID uint64, filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	// Get user's workspaces first
	workspaceFilter := &ports.WorkspaceFilter{
		UserID: &userID,
	}
	userWorkspaces, err := s.workspaceRepo.FindAll(workspaceFilter)
	if err != nil {
		return nil, err
	}

	// If user has no workspaces, return empty result
	if len(userWorkspaces) == 0 {
		return []*domain.Namespace{}, nil
	}

	// Create a map of user's workspace IDs for efficient lookup
	userWorkspaceIDs := make(map[uint64]bool)
	for _, workspace := range userWorkspaces {
		userWorkspaceIDs[workspace.ID] = true
	}

	// Get all clusters from user's workspaces
	clusterFilter := &ports.ClusterFilter{}
	allClusters, err := s.clusterRepo.FindAll(clusterFilter)
	if err != nil {
		return nil, err
	}

	// Create a map of cluster IDs that belong to user's workspaces
	userClusterIDs := make(map[uint64]bool)
	for _, cluster := range allClusters {
		if userWorkspaceIDs[cluster.WorkspaceID] {
			userClusterIDs[cluster.ID] = true
		}
	}

	// Get all namespaces and filter by user's clusters
	allNamespaces, err := s.namespaceRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Filter namespaces to only include those in user's clusters
	var userNamespaces []*domain.Namespace
	for _, namespace := range allNamespaces {
		if userClusterIDs[namespace.ClusterID] {
			userNamespaces = append(userNamespaces, namespace)
		}
	}

	return userNamespaces, nil
}

func (s *NamespaceService) GetByID(id uint64) (*domain.Namespace, error) {
	return s.namespaceRepo.FindByID(id)
}

func (s *NamespaceService) Update(id uint64, name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if slug == "" {
		return nil, errors.New("slug is required")
	}
	if clusterID == 0 {
		return nil, errors.New("cluster ID is required")
	}

	// Verify cluster exists
	_, err := s.clusterRepo.FindByID(clusterID, 0)
	if err != nil {
		return nil, errors.New("cluster not found")
	}

	// Find existing namespace
	namespace, err := s.namespaceRepo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields
	namespace.Name = name
	namespace.Slug = slug
	namespace.IsActive = isActive
	namespace.Type = Type
	namespace.ClusterID = clusterID

	err = s.namespaceRepo.Update(namespace)
	if err != nil {
		return nil, err
	}

	return namespace, nil
}

func (s *NamespaceService) Delete(id uint64) error {
	return s.namespaceRepo.Delete(id)
}
