package services

import (
	"fmt"
	"ops-api/internal/core/dto"
	"os"
	"os/exec"
	"path/filepath"
)

type CloudflareService struct {
	// Add any necessary dependencies or configurations here
}

func NewCloudflareService() *CloudflareService {
	return &CloudflareService{}
}

func (s *CloudflareService) GetAllZones() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get environment variables
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")

	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=cloudflare_api_token=%s", apiToken),
		fmt.Sprintf("-var=cloudflare_account_id=%s", accountId),
	}

	// Initialize Terraform only if needed
	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := "cloudflare"
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)
		return nil, tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// Run terraform apply
	fmt.Println("Running terraform apply...")
	applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
	tfApply := exec.Command("terraform", applyArgs...)
	tfApply.Dir = terraformDir
	tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()

	if tfApplyErr != nil {
		fmt.Printf("terraform apply failed - output: %s\n", string(tfApplyOutput))
		fmt.Printf("terraform apply failed - error: %v\n", tfApplyErr)
		return nil, tfApplyErr
	}

	fmt.Println("terraform apply successful")

	// Parse terraform apply output and convert to CloudflareResponse
	return s.parseTerraformApplyOutput(string(tfApplyOutput))
}

// parseTerraformApplyOutput parses terraform apply output and converts to CloudflareResponse
func (s *CloudflareService) parseTerraformApplyOutput(output string) (*dto.CloudflareResponse, error) {
	// First, try to get JSON output using terraform output command
	if response, err := s.getTerraformOutputFromCommand(); err == nil {
		return response, nil
	}

	// If that fails, use DTO parsing methods
	return dto.NewCloudflareResponseFromTerraformOutput(output)
}

// getTerraformOutputFromCommand gets terraform output using terraform output command
func (s *CloudflareService) getTerraformOutputFromCommand() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get terraform output in JSON format
	tfOutput := exec.Command("terraform", "output", "-json")
	tfOutput.Dir = terraformDir
	outputBytes, err := tfOutput.CombinedOutput()

	if err != nil {
		fmt.Printf("terraform output failed - output: %s\n", string(outputBytes))
		fmt.Printf("terraform output failed - error: %v\n", err)
		return nil, err
	}

	return dto.NewCloudflareResponseFromJSON(string(outputBytes))
}

// forceInitializeTerraform forces Terraform re-initialization
func (s *CloudflareService) forceInitializeTerraform(terraformDir string) error {
	fmt.Println("Force re-initializing Terraform...")

	// Remove .terraform directory to force clean init
	terraformStateDir := filepath.Join(terraformDir, ".terraform")
	if err := os.RemoveAll(terraformStateDir); err != nil {
		fmt.Printf("Warning: failed to remove .terraform directory: %v\n", err)
	}

	// Remove lock file
	lockFile := filepath.Join(terraformDir, ".terraform.lock.hcl")
	if err := os.Remove(lockFile); err != nil && !os.IsNotExist(err) {
		fmt.Printf("Warning: failed to remove lock file: %v\n", err)
	}

	// Run terraform init
	tfInit := exec.Command("terraform", "init", "-lock=false")
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()

	if tfInitErr != nil {
		fmt.Printf("terraform force init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform force init failed - error: %v\n", tfInitErr)
		return tfInitErr
	}

	fmt.Println("terraform force init successful")
	return nil
}
