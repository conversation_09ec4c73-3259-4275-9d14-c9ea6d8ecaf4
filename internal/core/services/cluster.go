package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
	"os"
	"os/exec"
)

type ClusterService struct {
	clusterRepo   ports.ClusterRepository
	workspaceRepo ports.WorkspaceRepository
}

func NewClusterService(clusterRepo ports.ClusterRepository, workspaceRepo ports.WorkspaceRepository) ports.ClusterService {
	return &ClusterService{
		clusterRepo:   clusterRepo,
		workspaceRepo: workspaceRepo,
	}
}

func (s *ClusterService) Create(name, region, poolName, size string, nodeCount, workspaceID uint64) (*domain.Cluster, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if region == "" {
		return nil, errors.New("region is required")
	}
	if poolName == "" {
		return nil, errors.New("pool name is required")
	}
	if size == "" {
		return nil, errors.New("size is required")
	}
	if nodeCount == 0 {
		return nil, errors.New("node count must be greater than 0")
	}

	// Check if cluster name already exists
	existingCluster, err := s.clusterRepo.FindByName(name)
	if err == nil && existingCluster != nil {
		return nil, errors.New("cluster name already exists")
	}

	// Verify workspace exists if workspace_id is provided
	if workspaceID > 0 {
		_, err = s.workspaceRepo.FindByID(workspaceID)
		if err != nil {
			return nil, errors.New("workspace not found")
		}
	}

	cluster := &domain.Cluster{
		Name:        name,
		Region:      region,
		PoolName:    poolName,
		Size:        size,
		NodeCount:   nodeCount,
		IsSelf:      false,
		WorkspaceID: workspaceID,
		StatusID:    1, // Default status ID, assuming 1 is "active"
	}

	err = s.clusterRepo.Insert(cluster)
	if err != nil {
		return nil, err
	}

	return cluster, nil
}

func (s *ClusterService) CreateWithUserValidation(userID uint64, name, region, poolName, size string, nodeCount, workspaceID uint64) (*domain.Cluster, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if region == "" {
		return nil, errors.New("region is required")
	}
	if poolName == "" {
		return nil, errors.New("pool name is required")
	}
	if size == "" {
		return nil, errors.New("size is required")
	}
	if nodeCount == 0 {
		return nil, errors.New("node count must be greater than 0")
	}

	// Check if cluster name already exists
	existingCluster, err := s.clusterRepo.FindByName(name)
	if err == nil && existingCluster != nil {
		return nil, errors.New("cluster name already exists")
	}

	// Verify workspace exists and belongs to the user
	if workspaceID > 0 {
		workspace, err := s.workspaceRepo.FindByID(workspaceID)
		if err != nil {
			return nil, errors.New("workspace not found")
		}

		// Check if the workspace belongs to the authenticated user
		if workspace.UserID != userID {
			return nil, errors.New("access denied: you can only create clusters in your own workspaces")
		}
	}

	cluster := &domain.Cluster{
		Name:        name,
		Region:      region,
		PoolName:    poolName,
		Size:        size,
		NodeCount:   nodeCount,
		IsSelf:      false,
		WorkspaceID: workspaceID,
		StatusID:    1, // Default status ID, assuming 1 is "active"
	}

	err = s.clusterRepo.Insert(cluster)
	if err != nil {
		return nil, err
	}

	return cluster, nil
}

func (s *ClusterService) CreateSelfCluster(userID uint64, workspaceID uint64) (*domain.Cluster, error) {
	// Verify workspace exists and belongs to the user
	if workspaceID > 0 {
		workspace, err := s.workspaceRepo.FindByID(workspaceID)
		if err != nil {
			return nil, errors.New("workspace not found")
		}

		if workspace.UserID != userID {
			return nil, errors.New("access denied: you can only create clusters in your own workspaces")
		}
	}

	cluster := &domain.Cluster{
		Name:          "k8s-blacking",
		Region:        "sgp1",
		PoolName:      "pool-18kvhkjw8",
		Size:          "s-4vcpu-8gb",
		NodeCount:     2,
		IsSelf:        true,
		WorkspaceID:   workspaceID,
		LoadBalanceIP: "*************",
		StatusID:      3, // Default status ID, assuming 1 is "active"
	}

	err := s.GetSelfCluster()
	if err != nil {
		return nil, err
	}

	err = s.clusterRepo.Insert(cluster)
	if err != nil {
		return nil, err
	}

	return cluster, nil
}

func (s *ClusterService) GetAll(filter *ports.ClusterFilter) ([]*domain.Cluster, error) {
	return s.clusterRepo.FindAll(filter)
}

func (s *ClusterService) GetAllByUserWorkspaces(userID uint64, filter *ports.ClusterFilter) ([]*domain.Cluster, error) {
	// Get user's workspaces first
	workspaceFilter := &ports.WorkspaceFilter{
		UserID: &userID,
	}
	userWorkspaces, err := s.workspaceRepo.FindAll(workspaceFilter)
	if err != nil {
		return nil, err
	}

	// If user has no workspaces, return empty result
	if len(userWorkspaces) == 0 {
		return []*domain.Cluster{}, nil
	}

	// Get all clusters and filter by user's workspaces
	allClusters, err := s.clusterRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Create a map of user's workspace IDs for efficient lookup
	userWorkspaceIDs := make(map[uint64]bool)
	for _, workspace := range userWorkspaces {
		userWorkspaceIDs[workspace.ID] = true
	}

	// Filter clusters to only include those in user's workspaces
	var userClusters []*domain.Cluster
	for _, cluster := range allClusters {
		if userWorkspaceIDs[cluster.WorkspaceID] {
			userClusters = append(userClusters, cluster)
		}
	}

	return userClusters, nil
}

func (s *ClusterService) GetByID(id uint64) (*domain.Cluster, error) {
	cluster, err := s.clusterRepo.FindByID(id, 0)
	if err != nil {
		return nil, err
	}
	if cluster == nil {
		return nil, errors.New("cluster not found")
	}
	return cluster, nil
}

func (s *ClusterService) Update(id uint64, name, region, poolName, size string, nodeCount, workspaceID, statusID uint64) (*domain.Cluster, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if region == "" {
		return nil, errors.New("region is required")
	}
	if poolName == "" {
		return nil, errors.New("pool name is required")
	}
	if size == "" {
		return nil, errors.New("size is required")
	}
	if nodeCount == 0 {
		return nil, errors.New("node count must be greater than 0")
	}
	if statusID == 0 {
		statusID = 1
	}

	cluster, err := s.clusterRepo.FindByID(id, 0)
	if err != nil {
		return nil, err
	}
	if cluster == nil {
		return nil, errors.New("cluster not found")
	}

	// Check if cluster name already exists (only if name is being changed)
	if cluster.Name != name {
		existingCluster, err := s.clusterRepo.FindByName(name)
		if err == nil && existingCluster != nil {
			return nil, errors.New("cluster name already exists")
		}
	}

	// Verify workspace exists if workspace_id is provided
	if workspaceID > 0 {
		_, err = s.workspaceRepo.FindByID(workspaceID)
		if err != nil {
			return nil, errors.New("workspace not found")
		}
	}

	cluster.Name = name
	cluster.Region = region
	cluster.PoolName = poolName
	cluster.Size = size
	cluster.NodeCount = nodeCount
	cluster.WorkspaceID = workspaceID
	cluster.StatusID = statusID

	err = s.clusterRepo.Update(cluster)
	if err != nil {
		return nil, err
	}

	return cluster, nil
}

func (s *ClusterService) UpdateLoadBalancerIP(id uint64, loadBalancerIP string) (*domain.Cluster, error) {
	// Find the existing cluster
	cluster, err := s.clusterRepo.FindByID(id, 0)
	if err != nil {
		return nil, err
	}
	if cluster == nil {
		return nil, errors.New("cluster not found")
	}

	// Update only the LoadBalanceIP field
	cluster.LoadBalanceIP = loadBalancerIP

	// Save the updated cluster
	err = s.clusterRepo.Update(cluster)
	if err != nil {
		return nil, err
	}

	return cluster, nil
}

func (s *ClusterService) Delete(id uint64) error {
	cluster, err := s.clusterRepo.FindByID(id, 0)
	if err != nil {
		return err
	}
	if cluster == nil {
		return errors.New("cluster not found")
	}

	return s.clusterRepo.Delete(id)
}

func (s *ClusterService) GetSelfCluster() error {
	terraformDir := "./terraform/self-cluster"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/self-cluster"
	}

	// Set environment variables for Terraform
	doToken := os.Getenv("DO_SELF_TOKEN")

	if err := os.Setenv("TF_VAR_do_token", doToken); err != nil {
		fmt.Println("set env do token failed", err)
		return err
	}

	// Initialize Terraform only if needed
	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := "self-cluster"
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)
		return tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// Run terraform apply
	fmt.Println("Running terraform apply...")
	tfApply := exec.Command("terraform", "apply", "-auto-approve")
	tfApply.Dir = terraformDir
	tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()

	if tfApplyErr != nil {
		fmt.Printf("terraform apply failed - output: %s\n", string(tfApplyOutput))
		fmt.Printf("terraform apply failed - error: %v\n", tfApplyErr)
		return tfApplyErr
	}

	fmt.Println("terraform apply successful")

	// Parse terraform apply output and convert to CloudflareResponse
	return nil
}
