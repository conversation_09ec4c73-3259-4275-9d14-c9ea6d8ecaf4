terraform {
  backend "pg" {
    conn_str             = ""
    schema_name          = ""
    skip_schema_creation = false
  }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

# Get an existing Kubernetes cluster by name
data "digitalocean_kubernetes_cluster" "example" {
  name = "k8s-blacking"
}

# Output cluster information
output "cluster_endpoint" {
  value = data.digitalocean_kubernetes_cluster.example.endpoint
}

output "cluster_status" {
  value = data.digitalocean_kubernetes_cluster.example.status
}

output "cluster_version" {
  value = data.digitalocean_kubernetes_cluster.example.version
}

output "kubeconfig" {
  value = data.digitalocean_kubernetes_cluster.example.kube_config[0].raw_config
  sensitive = true
}

resource "local_file" "kube-config" {
  content  = data.digitalocean_kubernetes_cluster.example.kube_config[0].raw_config
  filename = "../cluster/kubeconfig/k8s-blacking-kubeconfig.yaml"
}